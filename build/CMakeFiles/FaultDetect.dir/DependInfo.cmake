
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/project/fault_detect/src/camera/camera_manager.cpp" "CMakeFiles/FaultDetect.dir/src/camera/camera_manager.cpp.o" "gcc" "CMakeFiles/FaultDetect.dir/src/camera/camera_manager.cpp.o.d"
  "/home/<USER>/project/fault_detect/src/common.cpp" "CMakeFiles/FaultDetect.dir/src/common.cpp.o" "gcc" "CMakeFiles/FaultDetect.dir/src/common.cpp.o.d"
  "/home/<USER>/project/fault_detect/src/main.cpp" "CMakeFiles/FaultDetect.dir/src/main.cpp.o" "gcc" "CMakeFiles/FaultDetect.dir/src/main.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")

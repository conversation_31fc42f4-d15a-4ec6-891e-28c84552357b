# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/project/fault_detect

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/project/fault_detect/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/fault_detect/build/CMakeFiles /home/<USER>/project/fault_detect/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/project/fault_detect/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named FaultDetect

# Build rule for target.
FaultDetect: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 FaultDetect
.PHONY : FaultDetect

# fast build rule for target.
FaultDetect/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/build
.PHONY : FaultDetect/fast

src/camera/camera_manager.o: src/camera/camera_manager.cpp.o
.PHONY : src/camera/camera_manager.o

# target to build an object file
src/camera/camera_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/camera/camera_manager.cpp.o
.PHONY : src/camera/camera_manager.cpp.o

src/camera/camera_manager.i: src/camera/camera_manager.cpp.i
.PHONY : src/camera/camera_manager.i

# target to preprocess a source file
src/camera/camera_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/camera/camera_manager.cpp.i
.PHONY : src/camera/camera_manager.cpp.i

src/camera/camera_manager.s: src/camera/camera_manager.cpp.s
.PHONY : src/camera/camera_manager.s

# target to generate assembly for a file
src/camera/camera_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/camera/camera_manager.cpp.s
.PHONY : src/camera/camera_manager.cpp.s

src/common.o: src/common.cpp.o
.PHONY : src/common.o

# target to build an object file
src/common.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/common.cpp.o
.PHONY : src/common.cpp.o

src/common.i: src/common.cpp.i
.PHONY : src/common.i

# target to preprocess a source file
src/common.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/common.cpp.i
.PHONY : src/common.cpp.i

src/common.s: src/common.cpp.s
.PHONY : src/common.s

# target to generate assembly for a file
src/common.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/common.cpp.s
.PHONY : src/common.cpp.s

src/config_manager.o: src/config_manager.cpp.o
.PHONY : src/config_manager.o

# target to build an object file
src/config_manager.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/config_manager.cpp.o
.PHONY : src/config_manager.cpp.o

src/config_manager.i: src/config_manager.cpp.i
.PHONY : src/config_manager.i

# target to preprocess a source file
src/config_manager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/config_manager.cpp.i
.PHONY : src/config_manager.cpp.i

src/config_manager.s: src/config_manager.cpp.s
.PHONY : src/config_manager.s

# target to generate assembly for a file
src/config_manager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/config_manager.cpp.s
.PHONY : src/config_manager.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/FaultDetect.dir/build.make CMakeFiles/FaultDetect.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... FaultDetect"
	@echo "... src/camera/camera_manager.o"
	@echo "... src/camera/camera_manager.i"
	@echo "... src/camera/camera_manager.s"
	@echo "... src/common.o"
	@echo "... src/common.i"
	@echo "... src/common.s"
	@echo "... src/config_manager.o"
	@echo "... src/config_manager.i"
	@echo "... src/config_manager.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


#include "../include/common.h"
#ifdef USE_OPENCV
#include "../include/camera_manager.h"
#endif
#include <iostream>
#include <signal.h>

// 全局变量用于信号处理
std::atomic<bool> g_running(true);
#ifdef USE_OPENCV
std::unique_ptr<CameraManager> g_cameraManager;
#endif

// 信号处理函数
void signalHandler(int signal) {
    Utils::logInfo("接收到信号 " + std::to_string(signal) + "，准备退出...");
    g_running = false;
#ifdef USE_OPENCV
    if (g_cameraManager) {
        g_cameraManager->stopCapture();
    }
#endif
}

#ifdef USE_OPENCV
// 显示系统状态
void displaySystemStatus(const CameraManager& manager) {
    std::cout << "\n=== 系统状态 ===" << std::endl;
    
    auto cameraInfos = manager.getAllCameraInfo();
    for (const auto& info : cameraInfos) {
        std::cout << "摄像头 " << info.id << ": ";
        
        switch (info.status) {
            case CameraStatus::DISCONNECTED:
                std::cout << "未连接";
                break;
            case CameraStatus::CONNECTED:
                std::cout << "已连接";
                break;
            case CameraStatus::CAPTURING:
                std::cout << "采集中 (" << std::fixed << std::setprecision(1) 
                         << manager.getActualFPS(info.id) << " fps)";
                break;
            case CameraStatus::ERROR:
                std::cout << "错误: " << info.errorMsg;
                break;
        }
        
        if (info.status != CameraStatus::DISCONNECTED && info.status != CameraStatus::ERROR) {
            std::cout << " [" << info.width << "x" << info.height << "]";
        }
        
        std::cout << std::endl;
    }
    std::cout << std::endl;
}

// 测试单摄像头采集
bool testSingleCameraCapture(CameraManager& manager) {
    Utils::logInfo("开始测试单摄像头采集...");
    
    // 查找第一个可用摄像头
    int availableCameraId = -1;
    auto cameraInfos = manager.getAllCameraInfo();
    for (int i = 0; i < cameraInfos.size(); ++i) {
        if (manager.isCameraAvailable(i)) {
            availableCameraId = i;
            break;
        }
    }
    
    if (availableCameraId == -1) {
        Utils::logError("没有可用的摄像头进行测试");
        return false;
    }
    
    Utils::logInfo("使用摄像头 " + std::to_string(availableCameraId) + " 进行测试");
    
    // 创建输出目录
    std::string outputDir = "output/test_capture";
    Utils::createDirectory(outputDir);
    
    cv::Mat frame;
    int frameCount = 0;
    int maxFrames = 100; // 测试采集100帧
    
    auto startTime = std::chrono::steady_clock::now();
    
    while (g_running && frameCount < maxFrames) {
        if (manager.getFrame(availableCameraId, frame)) {
            frameCount++;
            
            // 每10帧保存一张图像
            if (frameCount % 10 == 0) {
                std::string filename = outputDir + "/frame_" + 
                                     std::to_string(frameCount) + ".jpg";
                if (Utils::saveImage(frame, filename)) {
                    Utils::logInfo("保存测试图像: " + filename);
                }
            }
            
            // 显示进度
            if (frameCount % 20 == 0) {
                auto currentTime = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    currentTime - startTime).count();
                double avgFPS = frameCount * 1000.0 / elapsed;
                
                Utils::logInfo("已采集 " + std::to_string(frameCount) + " 帧，" +
                              "平均帧率: " + std::to_string(avgFPS) + " fps");
            }
        } else {
            Utils::logWarning("获取帧失败，摄像头ID: " + std::to_string(availableCameraId));
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(1000 / Config::TARGET_FPS));
    }
    
    auto endTime = std::chrono::steady_clock::now();
    auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        endTime - startTime).count();
    double actualFPS = frameCount * 1000.0 / totalTime;
    
    Utils::logInfo("测试完成:");
    Utils::logInfo("  总帧数: " + std::to_string(frameCount));
    Utils::logInfo("  总时间: " + std::to_string(totalTime) + " ms");
    Utils::logInfo("  平均帧率: " + std::to_string(actualFPS) + " fps");
    Utils::logInfo("  目标帧率: " + std::to_string(Config::TARGET_FPS) + " fps");
    
    return frameCount > 0;
}

// 测试所有摄像头采集
bool testAllCamerasCapture(CameraManager& manager) {
    Utils::logInfo("开始测试所有摄像头采集...");
    
    std::vector<cv::Mat> frames;
    int testFrames = 50;
    int successCount = 0;
    
    for (int i = 0; i < testFrames && g_running; ++i) {
        if (manager.getAllFrames(frames)) {
            successCount++;
            
            // 保存第一组图像作为样本
            if (i == 0) {
                for (int j = 0; j < frames.size(); ++j) {
                    if (!frames[j].empty()) {
                        std::string filename = "output/test_all_cameras/camera_" + 
                                             std::to_string(j) + "_sample.jpg";
                        Utils::createDirectory("output/test_all_cameras");
                        Utils::saveImage(frames[j], filename);
                    }
                }
            }
        }
        
        if (i % 10 == 0) {
            Utils::logInfo("多摄像头测试进度: " + std::to_string(i) + "/" + std::to_string(testFrames));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1000 / Config::TARGET_FPS));
    }
    
    double successRate = (double)successCount / testFrames * 100.0;
    Utils::logInfo("多摄像头测试完成:");
    Utils::logInfo("  成功率: " + std::to_string(successRate) + "%");
    
    return successRate > 80.0; // 80%以上成功率认为测试通过
}
#endif // USE_OPENCV

int main() {
    Utils::logInfo("=== 拉吊索缺损识别系统 ===");
    Utils::logInfo("版本: 1.0.0");

#ifdef USE_OPENCV
    Utils::logInfo("OpenCV支持: 已启用");
    Utils::logInfo("开始启动系统...");

    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    try {
        // 初始化配置
        if (!Config::initializeConfig()) {
            Utils::logError("配置文件加载失败，使用默认配置");
        } else {
            Utils::logInfo("配置文件加载成功");
            Utils::logInfo("摄像头数量: " + std::to_string(Config::CAMERA_COUNT));
        }

        // 创建摄像头管理器
        g_cameraManager = std::make_unique<CameraManager>();

        // 初始化摄像头
        if (!g_cameraManager->initialize()) {
            Utils::logError("摄像头管理器初始化失败");
            return -1;
        }

        // 启动采集
        if (!g_cameraManager->startCapture()) {
            Utils::logError("启动摄像头采集失败");
            return -1;
        }

        // 显示系统状态
        displaySystemStatus(*g_cameraManager);

        // 运行测试
        Utils::logInfo("开始运行测试程序...");

        // 测试单摄像头采集
        if (!testSingleCameraCapture(*g_cameraManager)) {
            Utils::logError("单摄像头采集测试失败");
        }

        if (g_running) {
            // 测试所有摄像头采集
            if (!testAllCamerasCapture(*g_cameraManager)) {
                Utils::logWarning("多摄像头采集测试未完全通过");
            }
        }

        // 保持运行状态，等待用户中断
        Utils::logInfo("测试完成，系统保持运行状态...");
        Utils::logInfo("按 Ctrl+C 退出系统");

        while (g_running) {
            displaySystemStatus(*g_cameraManager);
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }

    } catch (const std::exception& e) {
        Utils::logError("系统运行异常: " + std::string(e.what()));
        return -1;
    }
#else
    Utils::logWarning("OpenCV支持: 未启用");
    Utils::logInfo("当前运行简化版本，仅测试基础功能");

    // 设置信号处理
    signal(SIGINT, signalHandler);

    // 测试基础功能
    Utils::logInfo("测试工具函数...");

    // 测试时间函数
    Utils::logInfo("当前时间: " + Utils::getCurrentTimeString());

    // 测试损伤类型转换
    for (int i = 0; i <= 7; ++i) {
        DamageType type = static_cast<DamageType>(i);
        Utils::logInfo("损伤类型 " + std::to_string(i) + ": " + Utils::damageTypeToString(type));
    }

    // 测试单位转换
    double pixels = 100.0;
    double mm = Utils::pixelToMm(pixels);
    double backToPixels = Utils::mmToPixel(mm);
    Utils::logInfo("像素转换测试: " + std::to_string(pixels) + " pixels = " +
                   std::to_string(mm) + " mm = " + std::to_string(backToPixels) + " pixels");

    // 测试目录创建
    if (Utils::createDirectory("output/test")) {
        Utils::logInfo("测试目录创建成功");
    }

    Utils::logInfo("基础功能测试完成");
    Utils::logInfo("要启用完整功能，请安装OpenCV并重新编译");

    // 等待用户中断
    Utils::logInfo("按 Ctrl+C 退出系统");
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
#endif

    Utils::logInfo("系统正常退出");
    return 0;
}

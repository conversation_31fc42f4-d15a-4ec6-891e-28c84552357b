#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <fstream>

int main() {
    std::cout << "=== 摄像头设备测试程序 ===" << std::endl;

    // 测试可用的摄像头设备
    std::vector<int> working_cameras;

    // 检查权限
    std::cout << "检查摄像头权限..." << std::endl;
    std::ifstream video0("/dev/video0");
    if (!video0) {
        std::cout << "警告: 无法访问 /dev/video0，可能是权限问题" << std::endl;
        std::cout << "请确保用户在video组中，可以运行以下命令：" << std::endl;
        std::cout << "  sudo usermod -a -G video $USER" << std::endl;
        std::cout << "  newgrp video" << std::endl;
    } else {
        video0.close();
        std::cout << "✓ 摄像头权限正常" << std::endl;
    }

    std::cout << "\n开始检测摄像头设备..." << std::endl;
    for (int i = 0; i < 10; ++i) {
        std::cout << "检测摄像头 " << i << "..." << std::endl;
        try {
            cv::VideoCapture cap(i);
            if (cap.isOpened()) {
                std::cout << "摄像头 " << i << " 可用" << std::endl;

                // 获取摄像头信息
                double width = cap.get(cv::CAP_PROP_FRAME_WIDTH);
                double height = cap.get(cv::CAP_PROP_FRAME_HEIGHT);
                double fps = cap.get(cv::CAP_PROP_FPS);

                std::cout << "  分辨率: " << width << "x" << height << std::endl;
                std::cout << "  帧率: " << fps << " FPS" << std::endl;

                // 尝试捕获一帧
                cv::Mat frame;
                if (cap.read(frame) && !frame.empty()) {
                    std::cout << "  ✓ 成功捕获图像: " << frame.cols << "x" << frame.rows << std::endl;
                    working_cameras.push_back(i);
                } else {
                    std::cout << "  ✗ 无法捕获图像" << std::endl;
                }

                cap.release();
            } else {
                std::cout << "摄像头 " << i << " 不可用" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "检测摄像头 " << i << " 时发生异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "\n=== 测试结果 ===" << std::endl;
    std::cout << "可工作的摄像头数量: " << working_cameras.size() << std::endl;
    
    if (working_cameras.empty()) {
        std::cout << "没有找到可工作的摄像头设备" << std::endl;
        return -1;
    }
    
    std::cout << "可工作的摄像头ID: ";
    for (int id : working_cameras) {
        std::cout << id << " ";
    }
    std::cout << std::endl;
    
    // 测试第一个可工作的摄像头
    int test_camera = working_cameras[0];
    std::cout << "\n=== 测试摄像头 " << test_camera << " 的实时捕获 ===" << std::endl;
    
    cv::VideoCapture cap(test_camera);
    if (!cap.isOpened()) {
        std::cout << "无法打开摄像头 " << test_camera << std::endl;
        return -1;
    }
    
    // 设置分辨率
    cap.set(cv::CAP_PROP_FRAME_WIDTH, 640);
    cap.set(cv::CAP_PROP_FRAME_HEIGHT, 480);
    cap.set(cv::CAP_PROP_FPS, 15);
    
    std::cout << "按任意键开始捕获测试（捕获5帧后自动停止）..." << std::endl;
    std::cin.get();
    
    for (int i = 0; i < 5; ++i) {
        cv::Mat frame;
        if (cap.read(frame) && !frame.empty()) {
            std::cout << "捕获第 " << (i+1) << " 帧: " << frame.cols << "x" << frame.rows 
                      << " 通道数: " << frame.channels() << std::endl;
        } else {
            std::cout << "捕获第 " << (i+1) << " 帧失败" << std::endl;
        }
        
        // 等待一小段时间
        cv::waitKey(100);
    }
    
    cap.release();
    std::cout << "\n测试完成！" << std::endl;
    
    return 0;
}
